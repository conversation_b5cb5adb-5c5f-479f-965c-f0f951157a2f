# 🚀 AWS云服务实现路线图 - 优化版

## 📋 项目概述

基于AWS云服务构建企业级跨域反向代理解决方案，实现高可用、高性能、安全可靠的网络访问服务。本版本优化了安全认证、自动化部署、日志监控以及多区域容灾等方面，并集成CI/CD流水线和测试验证环节。

## 🎯 总体架构

### 架构图
```
Internet → CloudFront → API Gateway → Lambda → Target Sites
    ↓           ↓            ↓          ↓
Route 53    WAF/Shield   Cognito    VPC/NAT
    ↓           ↓            ↓          ↓
  DNS解析    安全防护      身份认证    网络隔离
```

### 核心组件
- **CloudFront**: 全球CDN加速
- **API Gateway**: API管理和路由
- **Lambda**: 无服务器计算，现集成完善的JWT签名验证（通过Cognito公钥）
- **Route 53**: DNS服务
- **WAF**: Web应用防火墙
- **Cognito**: 身份认证
- **VPC**: 虚拟私有云
- **CI/CD流水线**: 利用AWS CodePipeline/Jenkins实现自动部署与版本迭代
- **自动化测试**: 集成单元测试、集成测试及压力测试，确保上线前的稳定性

## 📅 实施阶段

### 第一阶段：基础架构 (Week 1-2)

#### 1.1 AWS账户设置
```bash
# 创建AWS账户
# 配置IAM用户和角色，严格控制资源权限
# 设置计费告警与CloudTrail日志监控
```

#### 1.2 Lambda函数开发与安全增强
```python
# lambda_proxy.py
import json
import urllib3
import ssl
import os
import jwt  # 需要安装pyjwt
from urllib.parse import urlparse
from jwt import PyJWKClient

def lambda_handler(event, context):
    """AWS Lambda代理处理函数，强化JWT签名验证"""
    
    try:
        # 验证JWT签名
        token = event.get('headers', {}).get('Authorization', '')
        if token:
            token = token.replace('Bearer ', '')
            jwk_url = os.environ.get('COGNITO_JWK_URL')
            if not jwk_url:
                return error_response(500, "Missing Cognito JWK URL")
            jwk_client = PyJWKClient(jwk_url)
            signing_key = jwk_client.get_signing_key_from_jwt(token)
            jwt.decode(token, signing_key.key, algorithms=["RS256"], audience=os.environ.get('COGNITO_CLIENT_ID'))
        
        # 解析请求
        method = event.get('httpMethod', 'GET')
        path = event.get('path', '/')
        query_params = event.get('queryStringParameters') or {}
        headers = event.get('headers', {})
        body = event.get('body', '')
        
        # 获取目标URL
        target_domain = event.get('pathParameters', {}).get('domain')
        if not target_domain:
            return error_response(400, "Missing target domain")
        
        # 构建目标URL
        target_url = f"https://{target_domain}{path}"
        if query_params:
            query_string = '&'.join([f"{k}={v}" for k, v in query_params.items()])
            target_url += f"?{query_string}"
        
        # 安全检查
        if not is_allowed_domain(target_domain):
            return error_response(403, "Domain not allowed")
        
        # 发送请求
        response = make_secure_request(method, target_url, headers, body)
        
        return {
            'statusCode': response['status'],
            'headers': add_cors_headers(response['headers']),
            'body': response['body'],
            'isBase64Encoded': False
        }
        
    except Exception as e:
        return error_response(500, str(e))

def is_allowed_domain(domain):
    """检查域名是否在白名单中"""
    allowed_domains = os.environ.get('ALLOWED_DOMAINS', 'www.google.com,www.facebook.com,www.youtube.com,www.github.com').split(',')
    return domain in allowed_domains

def make_secure_request(method, url, headers, body):
    """发送安全请求"""
    http = urllib3.PoolManager(
        cert_reqs='CERT_REQUIRED',
        ca_certs=None,
        ssl_version=ssl.PROTOCOL_TLS
    )
    
    safe_headers = filter_headers(headers)
    safe_headers.update({
        'User-Agent': 'AWS-Lambda-Proxy/1.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
    })
    
    response = http.request(
        method=method,
        url=url,
        headers=safe_headers,
        body=body.encode('utf-8') if body else None,
        timeout=30
    )
    
    return {
        'status': response.status,
        'headers': dict(response.headers),
        'body': response.data.decode('utf-8')
    }

def filter_headers(headers):
    """过滤请求头"""
    filtered = {}
    allowed_headers = [
        'accept', 'accept-language', 'accept-encoding',
        'content-type', 'authorization'
    ]
    for key, value in headers.items():
        if key.lower() in allowed_headers:
            filtered[key] = value
    return filtered

def add_cors_headers(headers):
    """添加CORS头"""
    cors_headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': '*',
        'Access-Control-Max-Age': '86400'
    }
    result = dict(headers)
    result.update(cors_headers)
    return result

def error_response(status_code, message):
    """返回错误响应"""
    return {
        'statusCode': status_code,
        'headers': {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
        },
        'body': json.dumps({
            'error': message,
            'timestamp': 'n/a'
        }),
        'isBase64Encoded': False
    }
```

#### 1.3 部署脚本 & 测试验证
```bash
#!/bin/bash
# deploy_all.sh

echo "🚀 开始部署AWS代理服务..."

# 1. 部署Lambda函数
echo "📦 部署Lambda函数..."
./deploy_lambda.sh

# 2. 部署API Gateway
echo "🌐 部署API Gateway..."
sam deploy --guided

# 3. 部署CloudFront
echo "⚡ 部署CloudFront..."
python deploy_cloudfront.py

# 4. 配置WAF与安全组
echo "🛡️ 配置WAF和安全策略..."
aws wafv2 create-web-acl --cli-input-json file://waf_config.json

# 5. 设置监控与报警
echo "📊 配置CloudWatch监控与报警..."
aws cloudwatch put-metric-alarm --cli-input-json file://alarms.json

echo "✅ 部署完成! 开始运行自动化测试..."

# 6. 运行自动化测试脚本（需提前准备测试脚本）
python run_tests.py

echo "🌍 测试完成，请检查输出结果确认服务状态。"
```

### 第二阶段：API Gateway与CDN集成 (Week 3)

#### 2.1 API Gateway配置
（配置内容同前，但增加了对CI/CD流水线的支持与验证环节）

#### 2.2 部署API Gateway与CloudFront
（使用SAM与Python脚本自动化部署，并提供反馈信息用于测试验证）

### 第三阶段：安全与容灾 (Week 4-6)

- **安全策略增强**：完善WAF规则、升级Shield防护，严格验证JWT签名，并细化IAM权限。
- **多区域部署**：采用跨区域部署，借助Route 53实现智能流量分配和快速容灾。
- **日志与监控**：集中管理CloudWatch日志，并通过Logs Insights实现实时监控和报警。

### 第四阶段：持续集成与自动化测试

- 引入CI/CD流水线，自动化部署和回滚机制。
- 整合单元测试与集成测试（测试脚本见 run_tests.py），确保每次部署前均通过自动化测试。

## 💰 成本估算

（详见原文档，同时考虑优化后的安全、监控及多区域部署成本）

## 🎯 下一步计划

- 完成基础架构及安全模块优化；
- 建立CI/CD自动化流水线；
- 集成自动化测试并持续监测系统运行状态；
- 持续优化性能和扩展功能。

---

**备注**：请运行部署脚本 `deploy_all.sh` 以完成部署及自动化测试，确认服务运行状况。
